import React from "react";
import { PrimaryButton } from "@fluentui/react";
import styles from  "../Answer.module.css"
import { Agents } from "../../../api";

interface Props {
    choices: any;
    sanitizedAnswerHtml: string;
    callAgent: (choice: Agents, document_types: Array<string> | undefined) => void;

    
}

export const SingleSelection = ({ choices, callAgent, sanitizedAnswerHtml }: Props) => {
    // Select only one choice
    return (
        <div className={styles.buttonGroup}>
            
            {choices.map((choice:string, index:number) => (
                <PrimaryButton
                    style={{ marginRight: "20px" }}
                    key={index}
                    className={styles.choiceButton}
                    onClick={() => {
                        if (choice === Agents.rag) {
                            callAgent(Agents.rag, undefined);
                        } else if (choice === Agents.ragDocument) {
                            callAgent(Agents.ragDocument, undefined);
                        } else {
                            callAgent(Agents.textToSql, undefined);
                        }
                    }}
                >
                    {choice === Agents.rag 
                        ? "Search in document knowledge base" 
                        : choice === Agents.ragDocument 
                            ? "Get the whole document" 
                            : "Search into the database"
                    }
                </PrimaryButton>
            ))}
        </div>
    );
};