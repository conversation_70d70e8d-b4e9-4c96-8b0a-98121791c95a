# CHANGELOG

<!-- version list -->

## v1.0.2 (2025-07-04)

### Bug Fixes

- Now the user knows the environment
  ([`ac1e6cc`](https://bitbucket.org/electrolux-professional/compli-bot/commits/ac1e6ccafd21defb637ad5636014a6d5bcec10a5))

### Documentation

- Edit changelog
  ([`302921d`](https://bitbucket.org/electrolux-professional/compli-bot/commits/302921df938d40129cfbc765ee5af4395456382b))


## v1.0.1 (2025-07-03)

### Bug Fixes

- Now user can save personal settings

- Returned correct document type in the response of the rag_document tool

### Documentation

- Improved deploy documentation

### Refactoring

- Changed document type selection structure from string array to dict array


## v1.0.0 (2025-06-24)
- Production Release
