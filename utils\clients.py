import uuid
from typing import List, Tuple
import io
import base64
import asyncio
import threading
import concurrent.futures

import oracledb
from azure.storage.blob.aio import BlobServiceClient as BlobAsyncServiceClient
from azure.storage.blob import BlobServiceClient
from openai import AzureOpenAI
from langchain_openai import AzureChatOpenAI as LangChainAzureOpenAI

from config.config import BlobStorageConfig, Text2SQLConfig
from src.backend.contracts.chat_data import BotType
from src.agents.rag_agent.storage import get_rag_storage
from utils.core import get_logger
from utils.exceptions import DatabaseException

logger = get_logger(__file__)

class LangChainLLM:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._client = LangChainAzureOpenAI(*args, **kwargs)
        return cls._instance

    def __getattr__(self, name):
        return getattr(self._client, name)
    
class LangChainImageLLM:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._client = LangChainAzureOpenAI(*args, **kwargs)
        return cls._instance

    def __getattr__(self, name):
        return getattr(self._client, name)

class AzureLLM:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._client = AzureOpenAI(*args, **kwargs)
        return cls._instance

    def __getattr__(self, name):
        return getattr(self._client, name)
    
class AzureImageLLM:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._client = AzureOpenAI(*args, **kwargs)
        return cls._instance

    def __getattr__(self, name):
        return getattr(self._client, name)

class AzureEmbedder:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._client = AzureOpenAI(*args, **kwargs)
        return cls._instance

    def __getattr__(self, name):
        return getattr(self._client, name)


class KnowledgeBaseRegistry:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._init_knowledge_bases()
        return cls._instance

    def _init_knowledge_bases(self):
        bot_names = [
            BotType.CALL_CENTER_BOT.name,
            BotType.APPLICATION_HOWTO_BOT.name,
            BotType.JDAILO.name,
            BotType.SEO_BOT.name
        ]
        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = list(executor.map(get_rag_storage, bot_names))
        self.knowledge_bases = dict(zip(bot_names, results))

    def get(self, bot_name: str):
        return self.knowledge_bases.get(bot_name)

class DatabaseClient:

    def __init__(self) -> None:
        """
        Connects to the database client using environment parameters and keeps connection alive
        """
        oracledb.defaults.arraysize = 1000
        oracledb.defaults.fetch_lobs = False

        cfg = Text2SQLConfig()

        oracledb.init_oracle_client(
            lib_dir=rf"{cfg.oci_client_path}"
        )  # Path to Instant Client folder

        self.session = oracledb.create_pool(
            user=cfg.oci_username,
            password=cfg.oci_password,
            dsn=cfg.oci_dsn,
            min=0,
            max=200,
            increment=1,
        )

    def execute(self, query: str, numrows: int = 10) -> List[Tuple]:
        """Returns the list of tuples that satisfy the query provided in the database. Raises DatabaseException if the query is not valid.

        Args:
            query (str): The SQL query for the database

        Returns:
            List[Tuple]: The list of tuples that satisfy the query provided, with column headers
        """
        try:
            with self.session.acquire() as connection:
                with connection.cursor() as cursor:
                    cursor.execute(query)
                    # Add column names
                    result = [
                        tuple(
                            col_description[0] for col_description in cursor.description
                        )
                    ]
                    # Append the tuples resulting from the query
                    if numrows:
                        result += cursor.fetchmany(numRows=numrows)
                    else:
                        result += cursor.fetchall()
        except oracledb.DatabaseError as e:
            raise DatabaseException(e.__str__())

        return result

    def shutdown(self) -> None:
        """
        Closes the connection to the database
        """
        self.session.close(force=True)


class PreliminaryClient:

    def __init__(self, model: AzureOpenAI) -> None:
        self.cfg = Text2SQLConfig()

        self.client = model
        logger.info("Client initialised!")

    def infer(self, prompt: str) -> str:
        logger.debug("Invoking client...")
        result = self.client.chat.completions.create(
            model=self.cfg.preliminary_model_deployed,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            top_p=0.1,
        )
        logger.debug(f"Client has responded!")

        return result.choices[0].message.content or ""


class AzureBlobClient:

    def __init__(self, container_config: BlobStorageConfig, bot_name: str) -> None:
        """
        Create the istance of BlobServiceClient for write o read a blob in the blob storage
        """

        if bot_name == BotType.CALL_CENTER_BOT.name:

            self.account_url = container_config.call_center_bot_account_url
            self.account_name = container_config.call_center_bot_account_name
            self.account_key = container_config.call_center_bot_account_key
            self.container_name = container_config.call_center_bot_table_container
            self.container_image_name = container_config.call_center_bot_container_image

        elif bot_name == BotType.APPLICATION_HOWTO_BOT.name:

            self.account_url = container_config.how_to_bot_account_url
            self.account_name = container_config.how_to_bot_account_name
            self.account_key = container_config.how_to_bot_account_key
            self.container_name = container_config.how_to_bot_table_container
            self.container_image_name = container_config.how_to_bot_container_image

        elif bot_name == BotType.JDAILO.name:

            self.account_url = container_config.jdanallo_account_url
            self.account_name = container_config.jdanallo_account_name
            self.account_key = container_config.jdanallo_account_key
            self.container_name = container_config.jdanallo_table_container
            self.container_image_name = container_config.jdanallo_container_image

        elif bot_name == BotType.SEO_BOT.name:

            self.account_url = container_config.seobot_account_url
            self.account_name = container_config.seobot_account_name
            self.account_key = container_config.seobot_account_key
            self.container_name = container_config.seobot_table_container
            self.container_image_name = container_config.seobot_container_image

        self.storage_client = BlobServiceClient(
            account_url=self.account_url,
            account_name=self.account_name,
            credential=self.account_key,
        )

        self.storage_client_async = BlobAsyncServiceClient(
            account_url=self.account_url,
            account_name=self.account_name,
            credential=self.account_key,
        )

        self.semaphore = asyncio.Semaphore(10)

    async def write_table_blob(self, table_name: str, table_data: str) -> None:
        """Write a single table in the blob storage using a container client

        Args:
            table_name (str): the name that will be assigned to the blob
            table_data (str): the content of the blob

        Returns:
            None
        """

        blob_client = self.storage_client_async.get_container_client(self.container_name)
        check_client = self.storage_client_async.get_blob_client(
            self.container_name, table_name
        )
        #check_exist = await check_client.exists()
        # if check_exist :
        #     random_uid = str(uuid.uuid4())
        #     new_table_name = table_name + "_" + random_uid
        #     await blob_client.upload_blob(name=new_table_name, data=table_data)
        # else:
        await blob_client.upload_blob(name=table_name, data=table_data)



    async def write_table_batch(self, table_list: list):

        tasks = [self.write_table_blob(table['table_name'], table['table']) for table in table_list]
        await asyncio.gather(*tasks)


    def read_table_blob(self, blob_name: str) -> str:
        """Read a single table in the blob storage using a container client

        Args:
            blob_name (str): the name of the blob to be read
        Returns:
            str: the content of the blob
        """
        blob_client = self.storage_client.get_container_client(self.container_name)
        blob = blob_client.download_blob(blob_name).read().decode()

        return blob


    async def write_image_blob(self, image_name:str, image_data: bytes):


        blob_client = self.storage_client_async.get_container_client(self.container_image_name)
        check_client = self.storage_client_async.get_blob_client(
            self.container_name, image_name
        )
        # check_exist_image = await check_client.exists()
        # if check_exist_image:
        #     random_uid = str(uuid.uuid4())
        #     new_image_name = image_name + "_" + random_uid
        #     await blob_client.upload_blob(name=new_image_name, data=io.BytesIO(image_data), blob_type="BlockBlob", overwrite=True)
        # else:
        await blob_client.upload_blob(name=image_name, data= io.BytesIO(image_data), blob_type="BlockBlob", overwrite=True)

    async def write_image_batch(self, image_list: list):

        tasks = [self.write_image_blob(image['image_name'], image['image']) for image in image_list]
        await asyncio.gather(*tasks)


    def read_image_blob(self, blob_name: str) -> str:
        """Read a single imageks in the blob storage using a container client

        Args:
            blob_name (str): the name of the blob to be read
        Returns:
            str: the content of the blob
        """
        try:
            blob_client = self.storage_client.get_container_client(self.container_image_name)

            blob = blob_client.download_blob(blob_name)

            blob_data = blob.read()
            blob_creation_date = blob.properties["creation_time"]

            base_64_image = base64.b64encode(blob_data).decode('utf-8')
            mime_type = "image/jpg"  # Adjust the MIME type as needed
            base64_image_with_mime_type = f"data:{mime_type};base64,{base_64_image}"

            return base64_image_with_mime_type, blob_creation_date
        except Exception as e:
             logger.error("Error in reading image from blob storage")
             return None