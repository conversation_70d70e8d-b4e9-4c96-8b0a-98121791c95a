import React, { useEffect, useRef, useState } from 'react';
import { DefaultButton, Icon, IconButton, Image, ImageFit, Stack } from '@fluentui/react';
import './LoadImage.module.css';
import { AttachFilled } from '@fluentui/react-icons';

type Props = {
  onImagesSelected: (files: File[]) => void;
};

const ACCEPETD_IMAGE_TYPES = ["image/jpeg", "image/png", "image/webp"];
const MAX_FILE_SIZE_MB = 5;

export const UploadImage: React.FC<Props> = ({ onImagesSelected }) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [previews, setPreviews] = useState<{ file: File; url: string }[]>([]);

  const validateFiles = (files: FileList | null): File[] => {
    if (!files) return [];

    return Array.from(files).filter(file =>{
        const isValidType = ACCEPETD_IMAGE_TYPES.includes(file.type);
        const isValidSize = file.size <= MAX_FILE_SIZE_MB * 1024 * 1024 // Limit size is 5MB

        if(!isValidType){
            console.warn(`Invalid type: ${file.name}`);
        }

        if(!isValidSize){
            console.warn(`File too large: ${file.name}`)
        }

        return isValidType && isValidSize;
    });
  }

  const handleFiles = (files: FileList | null) => {
    if (!files) return;
    const validFiles = validateFiles(files)
    if (validFiles.length === 0) return;

    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) return;

    const newPreviews = imageFiles.map(file => ({
      file,
      url: URL.createObjectURL(file),
    }));

    setPreviews(prev => [...prev, ...newPreviews]);
    if (onImagesSelected) onImagesSelected(imageFiles);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };


  useEffect(() => {
    return () => {
      previews.forEach(p => URL.revokeObjectURL(p.url));
    };
  }, [previews]);

  return (
    <div
      className={`attach-image-button ${isDragging ? 'dragging' : ''}`}
      onDragOver={(e) => {
        e.preventDefault();
        setIsDragging(true);
      }}
      onDragLeave={() => setIsDragging(false)}
      onDrop={handleDrop}>
        
        <IconButton
        iconProps={{ iconName: 'Attach' }}
        onClick={() => fileInputRef.current?.click()}
        className="icon-only-button"
        title="Allega immagine"
        ariaLabel="Allega immagine"
       
        />
    
        <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        style={{ display: 'none' }}
        onChange={(e) => handleFiles(e.target.files)}
        />

    </div>
  );
};

export default UploadImage;
