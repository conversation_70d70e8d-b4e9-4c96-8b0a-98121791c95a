from enum import Enum
from typing import List, Optional

from src.agents.image_gen_agent.image_gen import ImageGen
from src.agents.text_to_sql_agent.text_to_sql import Text2SQL
from src.agents.rag_agent.rag import RAG
from src.agents.rag_agent.rag_document import RAGDocument
from src.agents.eproexcella_agent.eproexcella import EPROExcelLa 






class AgentFactory(Enum):
    TEXT_TO_SQL = 'text_to_sql'
    RAG = 'rag'
    RAG_DOCUMENT = 'rag_document'
    EPROEXCELLA = 'eproexcella'
    IMAGE_GEN = 'image_gen'

    def create_rag_agent(agent_name, bot_name, **kwargs):
        return RAG(bot_name, agent_name, **kwargs)

    def create_rag_document_agent(agent_name, bot_name, **kwargs):
        return RAGDocument(bot_name, agent_name, **kwargs)

    def create_text_to_sql_agent(agent_name, bot_name, **kwargs):
        return Text2SQL(bot_name, agent_name, **kwargs )

    def create_eproexcella(agent_name, bot_name, **kwargs):
        return EPROExcelLa(bot_name, agent_name, **kwargs)
    
    def create_imagegen(agent_name, bot_name, **kwargs):
        return ImageGen(bot_name, agent_name, **kwargs)
    
    def create(self,agent_name,  bot_name, **kwargs):

        if agent_name == AgentFactory.TEXT_TO_SQL.name:
            return self.create_text_to_sql_agent( agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.RAG.name:
            return self.create_rag_agent( agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.RAG_DOCUMENT.name:
            return self.create_rag_document_agent(agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.EPROEXCELLA.name:
            return self.create_eproexcella(agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.IMAGE_GEN.name:
            return self.create_imagegen(agent_name, bot_name, **kwargs)
