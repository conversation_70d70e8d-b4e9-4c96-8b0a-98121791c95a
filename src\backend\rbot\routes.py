import base64
import io
from pathlib import Path
import shutil
import threading
import json
import logging
import traceback
import requests
import zipfile
import os
import json
from itertools import tee
from typing import Tuple
from re import sub
from time import sleep, strftime, time
from sqlalchemy import asc
import pandas as pd
from flask import (
    Blueprint,
    Response,
    current_app,
    jsonify,
    make_response,
    request,
    session,
    send_file,
    abort
)
from flask.sessions import SessionMixin

from src import db, langchain_llm, azure_llm, text_to_sql_embedder, knowledge_bases
from src.agents.agent_factory import AgentFactory
from src.agents.answer import Answer as AgentAnswer
from src.agents.rag_agent.llm_models.metadata_extractor import MetadataExtractor
from src.agents.text_to_sql_agent.database.connection import DatabaseConnection
from src.backend.blueprints.auth.decorators import login_api, login_epr, admin_required
from src.backend.contracts.chat_data import (
    SelectionObj,
    Answer,
    AnswerFeedback,
    AnswerStatus,
    BotType,
    BotTypeResponse,
    ChatExtraDetails,
    ChatQuestion,
    ChatResponse,
    File,
    GenericResponse,
    ResponseStatus,
    UserRole,
    SQLAnswerType,
    RAGAnswerType
)
from src.backend.contracts.profile import UserProfile
from src.backend.db_managers import (
    update_feedback,
    update_or_create_question_history,
    update_search_settings,
    user_get_allowed_bots,
    update_history
)
from src.backend.models import QuestionHistory, User, UserRoles
from src.backend.queries import get_sample_questions
from src.backend.rbot.chat_service import ChatService
from src.backend.utils.strings import sample_answer
from src.bots.bot import CompliBot, CallCenterBot, HowToBot, EPROExcelLaBot, JDAIlo, SEOBot
from src.bots.conversation_session import ConversationSession
from src.common_tools.intent_detector.intent_detector import IntentDetector
from src.agents.text_to_sql_agent.text_to_sql import  implosion_query, text_to_sql
from src.agents.rag_agent.rag import rag
from src.agents.rag_agent.formatter import *
from src.agents.rag_agent.rag_document import rag_document
from src.common_tools.history.compilers import SQLAlchemyCompiler
from src.common_tools.history.history import ConversationHistory
from src.common_tools.history.parsers import SQLAlchemyParser
from utils.exceptions import IncapableException, SecurityException, StoppedException, UndetectedIntendException, DocumentNotFoundException
from src.backend.utils.sys_utils import stream_last_n_releases, get_env_version


rbot = Blueprint("rbot", __name__, static_folder="react_static")
stoppable = {}

current_dir = os.path.dirname(__file__)
parent_dir = os.path.abspath(os.path.join(current_dir, os.pardir))
grandparent_dir = os.path.abspath(os.path.join(current_dir, os.pardir, os.pardir))
UPLOAD_FOLDER = os.path.join(grandparent_dir, "agents\\eproexcella_agent\\upload")
GENERATED_IMAGE_FOLDER = os.path.join(grandparent_dir, "agents\\image_gen_agent\\generated")

# class BotFormatter(Formatter):
#     def __init__(self, fmt=None, datefmt=None, style='%'):
#         super().__init__(fmt, datefmt, style)
#         self.error_logger = logging.getLogger('formatter_errors')
#         self.error_logger.setLevel(logging.WARNING)
#         handler = logging.StreamHandler()  # You can also use FileHandler to log to a file
#         handler.setFormatter(logging.Formatter("[%(asctime)s] %(levelname)s: %(message)s"))
#         self.error_logger.addHandler(handler)
#         self.error_logger.propagate = False  # Prevents the error logger from using the same formatter

#     def format(self, record):
#         record.userId = None
#         record.conversationId = None
#         record.dialogId = None
#         if has_request_context() and request.is_json:
#             try:
#                 json_data = request.get_json(silent=True)
#                 if isinstance(json_data, dict):
#                     record.userId = json_data.get("user_id", "").split("@")[0]
#                     record.conversationId = json_data.get("conversation_id", "")[-4:]
#                     record.dialogId = json_data.get("dialog_id", "")[-4:]
#                 else:
#                     self.error_logger.warning("Invalid JSON payload: not a dictionary")
#             except (AttributeError, TypeError) as e:
#                 self.error_logger.warning(f"Unable to parse payload -> {e}")

#         return super().format(record)


# for handler in logging.root.handlers:
#     handler.setFormatter(BotFormatter("[%(asctime)s] {%(userId)s>%(conversationId)s>%(dialogId)s | %(thread)d.%(module)s.%(funcName)s:%(lineno)d} %(levelname)s: %(message)s"))

def is_valid_user(user_email: str, trusted_user_session: SessionMixin) -> bool:
    current_app.logger.debug(f"Checking user {user_email}...")
    return trusted_user_session["user"]["email"] == user_email

def is_valid_conversation(conversation_id, trusted_user_session) -> bool:
    current_app.logger.debug(f"Checking conversation {conversation_id}...")
    return QuestionHistory.query.filter_by(user_id=trusted_user_session["user"]["id"], conversation_id=conversation_id).first() is not None

def get_conversation_session_id(user_id, conversation_id) -> str:
    return f"conversation_session_{user_id}_{conversation_id}"

def get_user_role(user_id) -> UserRole:

    user_role = UserRoles.query.filter_by(user_id=user_id).first()
    #current_app.logger.info(f"User role of {session["user"]["email"]} = {user_role.role_id}")
    return UserRole(user_role.role_id)

def setup_conversation_session(u, cid):
    conversation_session = ConversationSession(u["email"], get_user_role(u["id"]))

    session_id = get_conversation_session_id(u["id"], cid)
    if session.get(session_id):
        conversation_session.update(session[session_id])

    stoppable[session_id] = conversation_session
    return conversation_session

def setup_history(u, cid):
    messages = QuestionHistory.query.filter_by(
        user_id=u["id"],
        conversation_id=cid,).filter(QuestionHistory.extra_details["status"].as_string() == AnswerStatus.SUCCESS.name).order_by(asc(QuestionHistory.created)).all()
    history = ConversationHistory(u["id"], messages, SQLAlchemyCompiler(SQLAlchemyParser()))
    return history

def delete_generated_images_dir():
    generation_images_dir = Path(GENERATED_IMAGE_FOLDER)
    for item in generation_images_dir.iterdir():
        if item.is_file() or item.is_symlink():
            item.unlink()  # Delete file or symbolic link
        elif item.is_dir():
            shutil.rmtree(item)

@rbot.before_request
def log_request_details():

    log_data = {
        'client_ip': request.remote_addr,
        'forwarded_ip': request.headers.get('X-Forwarded-For')
    }
    log_data['method'] = request.method,
    log_data['path'] = request.path
    if request.is_json:
        log_data['payload'] = request.json

    if current_app.logger.isEnabledFor(logging.DEBUG):

        current_app.logger.debug(f"Content-Type: {request.content_type}")
        current_app.logger.debug(f"Content-Length: {request.content_length}")
        current_app.logger.debug(f"Headers: {dict(request.headers)}")

        # Metodo 1: get_data() - legge tutto il body
        try:
            data1 = request.get_data()
            current_app.logger.debug(f"get_data(): {len(data1)} bytes")
            if data1:
                current_app.logger.debug(f"First 100 chars: {data1[:100]}")
        except Exception as e:
            current_app.logger.error(f"get_data() failed: {e}")

        # Metodo 2: Accesso diretto al form data (se form-encoded)
        try:
            if request.form:
                current_app.logger.debug(f"Form data: {dict(request.form)}")
        except Exception as e:
            current_app.logger.error(f"Form access failed: {e}")

        # Metodo 3: JSON data
        try:
            if request.is_json:
                json_data = request.get_json()
                current_app.logger.debug(f"JSON data: {json_data}")
        except Exception as e:
            current_app.logger.error(f"JSON access failed: {e}")

        # Metodo 4: Raw input stream (attenzione: consuma lo stream)
        try:
            # Solo se gli altri metodi falliscono
            if not data1:
                raw_data = request.stream.read(1024)  # Leggi max 1024 bytes
                current_app.logger.debug(f"Raw stream read: {len(raw_data)} bytes")
                if raw_data:
                    current_app.logger.debug(f"Raw data: {raw_data[:100]}")
        except Exception as e:
            current_app.logger.error(f"Raw stream read failed: {e}")

        log_data['user_agent'] = request.headers.get('User-Agent'),
        current_app.logger.debug("BOT request: %s", json.dumps(log_data))
    else:
        current_app.logger.info("BOT request: %s", json.dumps(log_data))


@rbot.route("/", defaults={"path": ""})
@login_epr
def index(path):
    try:
        current_app.logger.info(
            f"########## {session['user']['name']} logged in successfully"
        )
    except Exception as ex:
        current_app.logger.error(
            f"########## No user found in the flask session. Did authentication fail? {ex}"
        )
        raise
    return rbot.send_static_file("index.html")


@rbot.route("/assets/<path:rest_of_path>")
def assets(rest_of_path):
    return rbot.send_static_file(f"assets/{rest_of_path}")

@rbot.route("/insecure-chat", methods=["POST"])
def insecure_chat():
    try:
        current_app.logger.debug(request)
        current_app.logger.debug(request.headers)
        current_app.logger.debug(request.data)
    except Exception as e:
        current_app.logger.error(f'Unable to print request -> {e}')

    try:
        web_response = insecure_chat(request)
    except Exception as e:
        current_app.logger.error(f'Uncaught Exception {type(e)} -> {e}')
        web_response = ChatResponse()
        reply = "GENERIC ERROR: Cannot find an answer"
        web_response.answer = Answer(formatted_answer=reply)
        web_response.error = reply
        return jsonify(web_response.to_json()), 500

    return jsonify(web_response.to_json())


def insecure_chat(request) -> ChatResponse:

    cid = request.json.get("conversation_id")
    did = request.json.get("dialog_id")
    uid = request.json.get("user_id")
    pid: bool = request.json.get("preview")
    agent_name = request.json.get("agent_to_call")
    selected_bot = request.json.get("selected_bot")
    max_rows = 10
    show_explanation = request.json.get("overrides")["show_explanation"]
    show_sql = request.json.get("overrides")["show_sql"]
    user_db = User.query.filter(User.email.ilike(uid)).first()
    u = {'id': user_db.id, 'email': user_db.email}
    answer_type = request.json.get("overrides")["answer_type"]
    document_types = request.json.get("document_types")

    assert answer_type in SQLAnswerType._value2member_map_, (f" {answer_type} not supported as a type for sql answer.")
    # Setup history
    history = setup_history(u, cid)
    current_app.logger.info(f"{len(history.messages)} previous messages identified for user {uid}'s conversation {cid}")

    # Setup session
    session_id = get_conversation_session_id(u["id"], cid)
    conversation_session = setup_conversation_session(u, cid)

    start = time()
    web_response = ChatResponse(dialog_id=did)
    extra_details = ChatExtraDetails(bot_type=BotType.DUMMY.name)
    reply = None
    stream_answer = False
    submitted_text: str = request.json.get("dialog")

    #intent detector
    intent_detector = IntentDetector(langchain_llm, [rag, text_to_sql, rag_document])
    question_status = None
    try:
        match selected_bot:
            case BotType.DUMMY.name:
                extra_details.bot_type = BotType.DUMMY.name
                if submitted_text == 'ping!5':
                    sleep(5)
                return sample_answer(dialog_id=did, text=submitted_text)
            case BotType.APPLICATION_HOWTO_BOT.name:
                application_howto_bot = HowToBot(agents=[AgentFactory.RAG.name], bot_name=BotType.APPLICATION_HOWTO_BOT.name, model=langchain_llm)
                extra_details.bot_type = BotType.APPLICATION_HOWTO_BOT.name
                reply = application_howto_bot.call_agent(
                    AgentFactory.RAG.name, inquiry=submitted_text, history=history, stream=False, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)

                web_response.answer = Answer(
                    formatted_answer=reply.data,
                    #explanation="\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents]),
                    explanation = reply.explanation,
                    source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents])
                )
                web_response.classification = f"{reply.confidence*100:.0f}%" if reply else "🤯"
                web_response.images = reply.images
                current_app.logger.debug(f"Reply: {reply}")
            case BotType.COMPLI_BOT.name:
                compbot = CompliBot(agents=[AgentFactory.TEXT_TO_SQL.name], bot_name=BotType.COMPLI_BOT.name, azure_model=azure_llm, embedder_model=text_to_sql_embedder)
                extra_details.bot_type = BotType.COMPLI_BOT.name
                reply = compbot.call_agent(AgentFactory.TEXT_TO_SQL.name, inquiry=submitted_text, history=history, max_rows=max_rows, conversation_session=conversation_session, show_explanation=show_explanation, show_sql=show_sql)
                web_response.classification = f"{reply.confidence*100:.0f}%" if reply else "🤯"

                header = reply.data.pop(0)  # Extract the header
                # Create the table body
                df = pd.DataFrame(data=reply.data, columns=header)
                if answer_type == SQLAnswerType.MARKDOWN.value:
                    formatted_answer = df.to_markdown(index=False)
                    answer_type = SQLAnswerType.MARKDOWN.value
                elif answer_type == SQLAnswerType.HTML.value:
                    formatted_answer=df.to_html(index=False)
                    answer_type = SQLAnswerType.HTML.value
                elif answer_type == SQLAnswerType.JSON.value:
                    formatted_answer = json.loads(df.to_json(orient='split'))
                    answer_type = SQLAnswerType.JSON.value
                web_response.answer = Answer(
                    formatted_answer=formatted_answer,
                    query=reply.sql if get_user_role(u["id"]) == UserRole.ADMIN else None,
                    explanation=reply.explanation,
                    answer_type = answer_type
                )
                web_response.files = [File(name='FileName', path='FilePath')]
                current_app.logger.debug(f"Reply: {reply}")
            case BotType.CALL_CENTER_BOT.name:
                call_center_bot = CallCenterBot(agents=[AgentFactory.TEXT_TO_SQL.name, AgentFactory.RAG.name, AgentFactory.RAG_DOCUMENT.name],
                                                bot_name=BotType.CALL_CENTER_BOT.name,
                                                model=langchain_llm,
                                                azure_model=azure_llm,
                                                embedder_model= text_to_sql_embedder,
                                                knowledge_base = knowledge_bases.get(BotType.CALL_CENTER_BOT.name))
                extra_details.bot_type = BotType.CALL_CENTER_BOT.name

                if not agent_name and len(history.messages) > 0 and agent_name != AgentFactory.TEXT_TO_SQL.value: # In order to prevent an intent detection when the bot is asking for metadata.
                    last_history_message = history.messages[-1]
                    if last_history_message["role"] == "assistant" and last_history_message["agent"] == 'RAG':
                        agent_name = 'rag'
                    elif last_history_message["role"] == "assistant" and last_history_message["agent"] == 'RAG_DOCUMENT':
                        agent_name = 'rag_document'

                if agent_name == None: #Activate intent detecion
                    intent_detected = intent_detector.detect_intent(submitted_text)

                    if len(intent_detected) == 1:
                        agent_name = intent_detected[0]
                    elif len(intent_detected) == 0:  #If the intent is undetected, make a choice between all three agents
                        agent_name = "to_choice"
                        intent_detected = [AgentFactory.TEXT_TO_SQL.value, AgentFactory.RAG.value, AgentFactory.RAG_DOCUMENT.value]
                    else:
                        agent_name = "to_choice"

                web_response.question = submitted_text

                if agent_name  == AgentFactory.RAG.value:
                    reply, question_status, stream_answer = call_center_bot.call_agent(
                        AgentFactory.RAG.name, inquiry=submitted_text, history=history, stream=False,  conversation_session= conversation_session, max_chunks= 5, show_explanation=show_explanation, document_types = document_types)
                    if answer_type == RAGAnswerType.MARKDOWN.value:
                        formatted_answer = format_rag_to_markdown(reply.data)
                        answer_type = RAGAnswerType.MARKDOWN.value
                    elif answer_type == RAGAnswerType.HTML.value:
                        formatted_answer = format_rag_to_html(reply.data)
                        # formatted_answer = reply.data
                        answer_type = RAGAnswerType.HTML.value

                    web_response.answer = Answer(
                        formatted_answer=formatted_answer,
                        explanation = reply.explanation,
                        source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents]),
                        agent = AgentFactory.RAG.value
                    )
                    web_response.appendix = reply.appendix
                    web_response.classification = f"{
                        reply.confidence*100:.0f}%" if reply else "🤯"
                    web_response.images = reply.images
                    web_response.files = list(set([File(name= os.path.basename(doc.metadata["url"]), path = doc.metadata["url"]) for doc in reply.documents]))
                    if reply.document_types:
                        web_response.selection = SelectionObj(is_multiple=True, choices= reply.document_types)
                    current_app.logger.debug(f"Reply: {reply}")
                elif agent_name == AgentFactory.TEXT_TO_SQL.value:
                    stream_answer = False
                    reply = call_center_bot.call_agent(
                        AgentFactory.TEXT_TO_SQL.name, inquiry=submitted_text, history=history, max_rows=max_rows, conversation_session= conversation_session, show_explanation=show_explanation, show_sql=show_sql)
                    web_response.classification = f"{reply.confidence*100:.0f}%" if reply else "🤯"

                    header = reply.data.pop(0)  # Extract the header
                    # Create the table body
                    df = pd.DataFrame(data=reply.data, columns=header)
                    if answer_type == SQLAnswerType.MARKDOWN.value:
                        formatted_answer = df.to_markdown(index=False)
                        answer_type = SQLAnswerType.MARKDOWN.value
                    elif answer_type == SQLAnswerType.HTML.value:
                        formatted_answer=df.to_html(index=False)
                        answer_type = SQLAnswerType.HTML.value
                    elif answer_type == SQLAnswerType.JSON.value:
                        formatted_answer = json.loads(df.to_json(orient='split'))
                        answer_type = SQLAnswerType.JSON.value
                    web_response.answer = Answer(
                        formatted_answer=formatted_answer,
                        query=reply.sql if get_user_role(u["id"]) == UserRole.ADMIN else None,
                        explanation=reply.explanation,
                        answer_type = answer_type
                    )
                    web_response.files = []
                    current_app.logger.debug(f"Reply: {reply}")
                elif agent_name  == AgentFactory.RAG_DOCUMENT.value:
                    stream_answer = False
                    reply, question_status = call_center_bot.call_agent(
                        AgentFactory.RAG_DOCUMENT.name, inquiry=submitted_text, history=history, conversation_session= conversation_session, show_explanation=show_explanation)

                    if answer_type == RAGAnswerType.MARKDOWN.value:
                        formatted_answer = format_rag_document_to_markdown(reply.data)
                        answer_type = RAGAnswerType.MARKDOWN.value
                    elif answer_type == RAGAnswerType.HTML.value:
                        formatted_answer = format_rag_document_to_html(reply.data)
                        # formatted_answer = reply.data
                        answer_type = RAGAnswerType.HTML.value

                    web_response.answer = Answer(
                        formatted_answer=formatted_answer,
                        explanation = reply.explanation,
                        source_file = reply.source_file,
                        agent = AgentFactory.RAG_DOCUMENT.value,
                        answer_type = answer_type
                    )
                    web_response.source_file_details = reply.source_file_details
                    web_response.classification = f"{
                        reply.confidence*100:.0f}%" if reply else "🤯"
                    web_response.images = reply.images
                    current_app.logger.debug(f"Reply: {reply}")
                elif agent_name == "to_choice":
                    web_response.selection = SelectionObj(is_multiple=False, choices=intent_detected)
                    web_response.answer = Answer(formatted_answer="Where should I search this information?", agent="to_choice")
                    web_response.classification = "Not available"
                    current_app.logger.debug(f"Reply: {reply}")
            case BotType.EPROEXCELLA_BOT.name:
                eproexcella_bot = EPROExcelLaBot(agents=[AgentFactory.EPROEXCELLA.name], bot_name=BotType.EPROEXCELLA_BOT.name, user_id=user_db.id)
                extra_details.bot_type = BotType.EPROEXCELLA_BOT.name
                reply = eproexcella_bot.call_agent(AgentFactory.EPROEXCELLA.name, inquiry=submitted_text, history=history, preview=pid)
                data = pd.read_json(reply.data)
                data = data.head(10)
                web_response.answer = Answer(
                    formatted_answer=data.to_html(index=False)
                )
                web_response.suggested_classification = "Not available"
                current_app.logger.debug(f"Reply: {reply}")


    except IncapableException as e:
        current_app.logger.error(e)

        if e.failed_candidates is not None:
            reply: str = (
                "Here are the failed examples with their associated database errors:\n"
            )

            for wrong_sql, error_message in e.failed_candidates:
                reply += f"Failed SQL query:\n{wrong_sql}\nDatabase error message: {error_message}\n\n"

            web_response.answer = Answer(formatted_answer=reply)
        else:
            reply = e.message

        web_response.error = "ERROR: Cannot find an answer"
        web_response.answer = Answer(formatted_answer=reply)
        extra_details.status = AnswerStatus.ERROR
    except StoppedException:
        current_app.logger.debug("Stopped, clean exit.")
        reply = "Generation has been canceled"
        web_response.answer = Answer(formatted_answer=reply)
        extra_details.status = AnswerStatus.STOPPED

    except UndetectedIntendException:
        current_app.logger.debug(f"Can't detect intent")
        reply = "Can't detect intent, which feature do you want to use?"
        web_response.answer = Answer(
            formatted_answer= reply
        )
        extra_details.status = AnswerStatus.ERROR

    except DocumentNotFoundException:
        current_app.logger.debug("Document not found")
        reply = "Sorry, The document requested is not found"
        web_response.answer = Answer(
            formatted_answer=reply
        )
        extra_details.status = AnswerStatus.ERROR

    extra_details.duration = time() - start

    # Disable complibot stopping
    del stoppable[session_id]
    # Update user session with conversation session
    session[session_id] = conversation_session.save()

    threading.Thread(target=update_history, args = (stream_answer,reply, submitted_text, question_status, extra_details, u, cid, did,current_app._get_current_object(), )).start()

    return web_response

@rbot.route("/chat", methods=["POST"])
@login_api
def secure_chat_v2():

    try:
        chat_request_dto = {}
        chat_request_dto["overrides"] = request.json.get("overrides")
        chat_request_dto["conversation_id"] = request.json.get("conversation_id")
        chat_request_dto["user_id"] = request.json.get("user_id")
        chat_request_dto["preview"] = request.json.get("preview")
        chat_request_dto["agent_name"] = request.json.get("agent_to_call")
        chat_request_dto["document_types"] = request.json.get("document_types")
        chat_request_dto["dialog_id"] = request.json.get("dialog_id")
        chat_request_dto["images"] = request.json.get("images")

        selected_bot = session['selected_bot']

        user = session['user']
        chat_request_dto["user"] = user
        uid = session["user"]["id"]
        u_role = get_user_role(uid)

        # delete_generated_images_dir()

        if not is_valid_user(request.json.get("user_id"), session):
            current_app.logger.warning(f"User {uid} is not authorized!")
            raise SecurityException(f"Cannot find an answer due to user error")

        user_db = User.query.filter(User.email.ilike(user["email"])).first()
        update_search_settings(db.session, user_db, request.json.get(
            "overrides"))  # update user settings
        max_rows = user_db.user_settings["search_settings"]["top"]


        query = request.json.get("dialog")

        cid = request.json.get("conversation_id")
        did = request.json.get("dialog_id")

        session_id = get_conversation_session_id(user["id"], cid)
        conversation_session = setup_conversation_session(user, cid)

        chat_service = ChatService(current_app.logger)
        web_response, stream, extra_details, question_status, reply = chat_service.chat(chat_request_dto, query, selected_bot,conversation_session, max_rows )

        if get_user_role(user["id"]) != UserRole.ADMIN:
            web_response.answer.query = None

    except Exception as e:

        current_app.logger.error(f'Uncaught Exception {type(e)} -> {e}')
        current_app.logger.error(traceback.format_exc())
        web_response = chat_service.build_unhandled_error_response(did, selected_bot, u_role)

    threading.Thread(target=update_history, args = (stream, reply, query, question_status, extra_details, user, cid, did,current_app._get_current_object(), )).start()

    # Disable bot stopping
    del stoppable[session_id]
    # Update user session with conversation session
    session[session_id] = conversation_session.save()
    

    if stream:
        return Response(chat_service.stream_tokens(web_response), content_type='text/event-stream')
    else:
        return jsonify(web_response.to_json())

# FIXME - Refactor this method in conjunction with chat(request) ASAP
@rbot.route("/chat_v1", methods=["POST"])
@login_api
def secure_chat():

    try:
        web_response, stream = chat(request)
        # print(web_response.answer.formatted_answer)
        # print(type(web_response.answer.formatted_answer))
    except Exception as e:
        uid = session["user"]["id"]
        cid = request.json.get("conversation_id")
        did = request.json.get("dialog_id")
        u_role = get_user_role(uid)

        web_response = ChatResponse(dialog_id=request.json.get("dialog_id"))
        extra_details = ChatExtraDetails(bot_type=BotType[session["selected_bot"]].name)
        current_app.logger.error(f'Uncaught Exception {type(e)} -> {e}')
        current_app.logger.error(traceback.format_exc())

        match u_role:
            case UserRole.USER:
                reply = "GENERIC ERROR: Cannot find an answer"
            case UserRole.ADMIN:
                reply = traceback.format_exc()

        web_response.answer = Answer(formatted_answer=reply)
        web_response.error = reply
        extra_details.status = AnswerStatus.ERROR

        update_or_create_question_history(
        uid,
        cid,
        did,
        question=ChatQuestion(question=request.json.get("dialog")).to_json(),
        raw_response=reply.to_json() if isinstance(reply, AgentAnswer) else reply,
        extra_details=extra_details.to_json()
        )
    if stream:
        chat_history = ''
        # @stream_with_context
        def stream_tokens():
            """ Generator of stream tokens """
            formatted_answer = web_response.answer.formatted_answer
            web_response.answer.formatted_answer = ""
            # web_response.answer.source_file = ""

            json_web_answer = web_response.to_json()

            yield f"{json.dumps({'v': json_web_answer})}\n\n"
            sleep(0.005)
            for chunk in formatted_answer:
                yield f"{json.dumps({'v': chunk})}\n\n"
                sleep(0.005)
                # yield chunk
            # yield "data: [DONE]\n\n"

        return Response(stream_tokens(), content_type='text/event-stream')
    else:
        return jsonify(web_response.to_json())




# FIXME - Refactor this method in conjunction with secure_chat() ASAP
def chat(request) -> Tuple[ChatResponse, bool]:
    u = session["user"]

    cid = request.json.get("conversation_id")
    did = request.json.get("dialog_id")
    uid = request.json.get("user_id")
    pid: bool = request.json.get("preview")
    agent_name = request.json.get("agent_to_call")
    show_explanation = request.json.get("overrides")["show_explanation"]
    show_sql = request.json.get("overrides")["show_sql"]
    answer_type = request.json.get("overrides")["answer_type"]

    assert answer_type in SQLAnswerType._value2member_map_, (f" {answer_type} not supported as a type for sql answer.")
    document_types = request.json.get("document_types")

    if not is_valid_user(uid, session): #da spostare sul controller
        current_app.logger.warning(f"User {uid} is not authorized!")
        raise SecurityException(f"Cannot find an answer due to user error")

    user_db = User.query.filter(User.email.ilike(u["email"])).first()
    update_search_settings(db.session, user_db, request.json.get(
        "overrides"))  # update user settings
    max_rows = user_db.user_settings["search_settings"]["top"]

    # Setup history
    history = setup_history(u, cid)
    current_app.logger.info(f"{len(history.messages)} previous messages identified for user {uid}'s conversation {cid}")

    # Setup session
    session_id = get_conversation_session_id(u["id"], cid)
    conversation_session = setup_conversation_session(u, cid)

    start = time()
    web_response = ChatResponse(dialog_id=did)
    extra_details = ChatExtraDetails(bot_type=BotType.DUMMY.name)
    reply = None
    stream_answer = False
    submitted_text: str = request.json.get("dialog")

    #intent detector
    intent_detector = IntentDetector(langchain_llm, [rag, text_to_sql, rag_document])
    question_status = None
    try:
        match session['selected_bot']:
            case BotType.DUMMY.name:
                extra_details.bot_type = BotType.DUMMY.name
                if submitted_text == 'ping!5':
                    sleep(5)
                return sample_answer(dialog_id=did, text=submitted_text), stream_answer
            case BotType.APPLICATION_HOWTO_BOT.name:
                stream_answer = True
                application_howto_bot = HowToBot(agents=[AgentFactory.RAG.name], bot_name=BotType.APPLICATION_HOWTO_BOT.name, model=langchain_llm)
                extra_details.bot_type = BotType.APPLICATION_HOWTO_BOT.name
                reply = application_howto_bot.call_agent(
                    AgentFactory.RAG.name, inquiry=submitted_text, history=history, stream=True, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)
                if stream_answer:
                    reply.data, answer = tee(reply.data, 2)
                    reply.data = list(reply.data)
                web_response.answer = Answer(
                    formatted_answer=reply.data,
                    #explanation="\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents]),
                    explanation = reply.explanation,
                    source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents])
                )
                web_response.classification = f"{
                    reply.confidence*100:.0f}%" if reply else "🤯"
                web_response.images = reply.images
                current_app.logger.debug(f"Reply: {reply}")
            case BotType.COMPLI_BOT.name:
                compbot = CompliBot(agents=[AgentFactory.TEXT_TO_SQL.name], bot_name=BotType.COMPLI_BOT.name, azure_model=azure_llm, embedder_model=text_to_sql_embedder)
                extra_details.bot_type = BotType.COMPLI_BOT.name #DA METTERE NEL SERVICE
                reply = compbot.call_agent(
                    AgentFactory.TEXT_TO_SQL.name, inquiry=submitted_text, history=history, max_rows=max_rows, conversation_session=conversation_session, show_explanation=show_explanation, show_sql=show_sql)
                web_response.classification = f"{
                    reply.confidence*100:.0f}%" if reply else "🤯"
                header = reply.data.pop(0)  # Extract the header
                # Create the table body
                df = pd.DataFrame(data=reply.data, columns=header)
                if answer_type == SQLAnswerType.MARKDOWN.value:
                    formatted_answer = df.to_markdown(index=False)
                    answer_type = SQLAnswerType.MARKDOWN.value
                elif answer_type == SQLAnswerType.HTML.value:
                    formatted_answer=df.to_html(index=False)
                    answer_type = SQLAnswerType.HTML.value
                elif answer_type == SQLAnswerType.JSON.value:
                    formatted_answer = json.loads(df.to_json(orient='split'))
                    answer_type = SQLAnswerType.JSON.value
                web_response.answer = Answer(
                    formatted_answer=formatted_answer,
                    query=reply.sql if get_user_role(u["id"]) == UserRole.ADMIN else None,
                    explanation=reply.explanation,
                    answer_type = answer_type,
                    agent =  AgentFactory.TEXT_TO_SQL.value
                )
                web_response.files = [File(name='FileName', path='FilePath')]
                current_app.logger.debug(f"Reply: {reply}")
            case BotType.CALL_CENTER_BOT.name:
                call_center_bot = CallCenterBot(agents=[AgentFactory.TEXT_TO_SQL.name, AgentFactory.RAG.name, AgentFactory.RAG_DOCUMENT.name],
                                                bot_name=BotType.CALL_CENTER_BOT.name,
                                                model=langchain_llm,
                                                azure_model=azure_llm,
                                                embedder_model= text_to_sql_embedder,
                                                knowledge_base = knowledge_bases.get(BotType.CALL_CENTER_BOT.name))
                extra_details.bot_type = BotType.CALL_CENTER_BOT.name

                if not agent_name and len(history.messages) > 0 and agent_name != AgentFactory.TEXT_TO_SQL.value: # In order to prevent an intent detection when the bot is asking for metadata.
                    last_history_message = history.messages[-1]
                    if last_history_message["role"] == "assistant" and last_history_message["agent"] == 'RAG':
                        agent_name = 'rag'
                    elif last_history_message["role"] == "assistant" and last_history_message["agent"] == 'RAG_DOCUMENT':
                        agent_name = 'rag_document'

                if agent_name == None: #Activate intent detecion
                    intent_detected = intent_detector.detect_intent(submitted_text)

                    if len(intent_detected) == 1:
                        agent_name = intent_detected[0]
                    elif len(intent_detected) == 0:  #If the intent is undetected, make a choice between all three agents
                        agent_name = "to_choice"
                        intent_detected = [AgentFactory.TEXT_TO_SQL.value, AgentFactory.RAG.value, AgentFactory.RAG_DOCUMENT.value]
                    else:
                        agent_name = "to_choice"

                web_response.question = submitted_text

                if agent_name  == AgentFactory.RAG.value:
                    reply, question_status, stream_answer = call_center_bot.call_agent(
                        AgentFactory.RAG.name, inquiry=submitted_text, history=history, stream=True,  conversation_session= conversation_session, max_chunks= 5, show_explanation=show_explanation, document_types = document_types)

                    web_response.answer = Answer(
                        formatted_answer = reply.data,
                        explanation = reply.explanation,
                        source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents]),
                        agent = AgentFactory.RAG.value
                    )
                    web_response.appendix = reply.appendix
                    web_response.classification = f"{
                        reply.confidence*100:.0f}%" if reply else "🤯"
                    web_response.images = reply.images
                    web_response.files = list(set([File(name= os.path.basename(doc.metadata["url"]), path = doc.metadata["url"]) for doc in reply.documents]))
                    if reply.document_types:
                        web_response.selection = SelectionObj(is_multiple=True, choices= reply.document_types)
                    current_app.logger.debug(f"Reply: {reply}")
                elif agent_name == AgentFactory.TEXT_TO_SQL.value:
                    stream_answer = False
                    intent_detector = IntentDetector(langchain_llm, [implosion_query])
                    intent_detected = intent_detector.detect_query_intent(submitted_text)

                    if len(intent_detected) == 0:
                        asking_for_implosion = False
                    else:
                        asking_for_implosion = True
                        metadata_extractor = MetadataExtractor(langchain_llm)
                        code_extracted = metadata_extractor.get_code_from_query(submitted_text)
                        sparse_or_item = metadata_extractor.get_item_or_sparse_from_query(submitted_text)
                        current_app.logger.info(f"The code extracted is:\n{code_extracted}")

                    reply = call_center_bot.call_agent(
                        AgentFactory.TEXT_TO_SQL.name, inquiry=submitted_text, history=history, max_rows=max_rows, conversation_session= conversation_session, show_explanation=show_explanation, show_sql=show_sql, specific_query=asking_for_implosion)
                    web_response.classification = f"{
                        reply.confidence*100:.0f}%" if reply else "🤯"

                    header = reply.data.pop(0)  # Extract the header
                    # Create the table body
                    df = pd.DataFrame(data=reply.data, columns=header)
                    if answer_type == SQLAnswerType.MARKDOWN.value:
                        formatted_answer = df.to_markdown(index=False)
                        answer_type = SQLAnswerType.MARKDOWN.value
                    elif answer_type == SQLAnswerType.HTML.value:
                        formatted_answer=df.to_html(index=False)
                        answer_type = SQLAnswerType.HTML.value
                    elif answer_type == SQLAnswerType.JSON.value:
                        formatted_answer = json.loads(df.to_json(orient='split'))
                        answer_type = SQLAnswerType.JSON.value
                    web_response.answer = Answer(
                        formatted_answer=formatted_answer,
                        query=reply.sql if get_user_role(u["id"]) == UserRole.ADMIN else None,
                        explanation=reply.explanation,
                        answer_type = answer_type,
                        agent =  AgentFactory.TEXT_TO_SQL.value
                    )
                    web_response.files = []
                    current_app.logger.debug(f"Reply: {reply}")
                elif agent_name  == AgentFactory.RAG_DOCUMENT.value:
                    stream_answer = False
                    current_app.logger.info("Calling RAG_DOCUMENT")
                    reply, question_status = call_center_bot.call_agent(
                        AgentFactory.RAG_DOCUMENT.name, inquiry=submitted_text, history=history, conversation_session= conversation_session, show_explanation=show_explanation)
                    current_app.logger.info("eNDEND t2s agent!")
                    web_response.answer = Answer(
                        formatted_answer=reply.data,
                        explanation = reply.explanation,
                        source_file = reply.source_file,
                        agent = AgentFactory.RAG_DOCUMENT.value
                    )
                    web_response.source_file_details = reply.source_file_details
                    web_response.classification = f"{
                        reply.confidence*100:.0f}%" if reply else "🤯"
                    web_response.images = reply.images
                    current_app.logger.debug(f"Reply: {reply}")
                elif agent_name == "to_choice":
                    web_response.selection = SelectionObj(is_multiple=False, choices=intent_detected)
                    web_response.answer = Answer(formatted_answer="Where should I search this information?", agent="to_choice")
                    web_response.classification = "Not available"
            case BotType.JDAILO.name:
                stream_answer = True
                jdailo = JDAIlo(agents=[AgentFactory.RAG.name],
                                bot_name=BotType.JDAILO.name,
                                model=langchain_llm,
                                knowledge_base=knowledge_bases.get(BotType.JDAILO.name))
                extra_details.bot_type = BotType.JDAILO.name
                reply = jdailo.call_agent(
                    AgentFactory.RAG.name, inquiry=submitted_text, history=history, stream=True, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)

                web_response.answer = Answer(
                    formatted_answer=reply.data,
                    explanation = reply.explanation,
                    agent = AgentFactory.RAG.value,
                    source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents])
                )
                web_response.classification = f"{
                    reply.confidence*100:.0f}%" if reply else "🤯"
                web_response.images = reply.images
                current_app.logger.debug(f"Reply: {reply}")
            case BotType.SEO_BOT.name:
                stream_answer = True
                seobot = SEOBot(agents=[AgentFactory.RAG.name],
                                bot_name=BotType.SEO_BOT.name,
                                model=langchain_llm,
                                knowledge_base=knowledge_bases.get(BotType.SEO_BOT.name))
                extra_details.bot_type = BotType.SEO_BOT.name
                reply = seobot.call_agent(
                    AgentFactory.RAG.name, inquiry=submitted_text, history=history, stream=True, max_chunks=5, conversation_session=conversation_session, show_explanation=show_explanation)

                web_response.answer = Answer(
                    formatted_answer=reply.data,
                    explanation = reply.explanation,
                    agent = AgentFactory.RAG.value,
                    source_file = "\n\n".join([f"From {os.path.basename(doc.metadata["url"])}: \n {sub(r"[\s]+", " ", doc.page_content)}" for doc in reply.documents])
                )
                web_response.classification = f"{
                    reply.confidence*100:.0f}%" if reply else "🤯"
                web_response.images = reply.images
                current_app.logger.debug(f"Reply: {reply}")
            case BotType.EPROEXCELLA_BOT.name:
                eproexcella_bot = EPROExcelLaBot(agents=[AgentFactory.EPROEXCELLA.name], bot_name=BotType.EPROEXCELLA_BOT.name, user_id=user_db.id)
                extra_details.bot_type = BotType.EPROEXCELLA_BOT.name
                reply = eproexcella_bot.call_agent(AgentFactory.EPROEXCELLA.name, inquiry=submitted_text, history=history, preview=pid)
                data = pd.read_json(reply.data)
                data = data.head(10)
                web_response.answer = Answer(
                    formatted_answer=data.to_html(index=False)
                )
                web_response.suggested_classification = "Not available"
                current_app.logger.debug(f"Reply: {reply}")


    except IncapableException as e:
        current_app.logger.error(e)

        if e.failed_candidates is not None:
            reply: str = (
                "Here are the failed examples with their associated database errors:\n"
            )

            for wrong_sql, error_message in e.failed_candidates:
                reply += f"Failed SQL query:\n{wrong_sql}\nDatabase error message: {error_message}\n\n"

            web_response.answer = Answer(formatted_answer=reply)
        else:
            reply = e.message

        web_response.error = "ERROR: Cannot find an answer"
        web_response.answer = Answer(formatted_answer=reply)
        extra_details.status = AnswerStatus.ERROR
    except StoppedException:
        current_app.logger.debug("Stopped, clean exit.")
        reply = "Generation has been canceled"
        web_response.answer = Answer(formatted_answer=reply)
        extra_details.status = AnswerStatus.STOPPED

    except UndetectedIntendException:
        current_app.logger.debug(f"Can't detect intent")
        reply = "Can't detect intent, which feature do you want to use?"
        web_response.answer = Answer(
            formatted_answer= reply
        )
        extra_details.status = AnswerStatus.ERROR

    except DocumentNotFoundException:
        current_app.logger.debug("Document not found")
        reply = "Sorry, The document requested is not found"
        web_response.answer = Answer(
            formatted_answer=reply
        )
        extra_details.status = AnswerStatus.ERROR

    extra_details.duration = time() - start

    # Disable complibot stopping
    del stoppable[session_id]
    # Update user session with conversation session
    session[session_id] = conversation_session.save()


    threading.Thread(target=update_history, args = (stream_answer,reply, submitted_text, question_status, extra_details, u, cid, did,current_app._get_current_object(), )).start()

    return web_response, stream_answer

@rbot.route("/chat/<path:conversation_id>", methods=["DELETE"])
@login_api
def stop(conversation_id):
    if is_valid_conversation(conversation_id, session): # FIXME - This security check fails if the user invokes stop on the first message of a conversation
        current_app.logger.info(f"User {session["user"]["email"]}'s conversation {conversation_id} exists!")
        key = get_conversation_session_id(session["user"]["id"], conversation_id)

        conversation_session: ConversationSession = stoppable.get(key)

        if conversation_session:
            conversation_session.stop()

        return Response(status=200)
    else:
        current_app.logger.warning(f"User {session["user"]["email"]}'s conversation {conversation_id} does not exist!")
        raise SecurityException("Cannot find an answer due to conversation error")


@rbot.route("/export-data/<path:conversation_id>/<path:dialog_id>", methods=["GET"])
@login_api
def export_data(conversation_id, dialog_id):
    u = session["user"]
    try:
        question_history = QuestionHistory.query.filter_by(
            user_id=u["id"],
            conversation_id=conversation_id,
            dialog_id=dialog_id
        ).first()

        # Creating output and writer (pandas Excel writer)
        out = io.BytesIO()
        writer = pd.ExcelWriter(out, engine='xlsxwriter')

        database = DatabaseConnection()

        if "sql" in question_history.response:
            sql = question_history.response["sql"]
            query_result = database.validate(sql, max_rows=5000)
            data_to_export = query_result[1] if query_result is not None else pd.DataFrame()
            df = pd.DataFrame(data=data_to_export[1:], columns=data_to_export[0])
            # Export data frame to Excel
            df.to_excel(excel_writer=writer, index=False, sheet_name='Your Data')

            # Export question to Excel
            df_details = pd.DataFrame({
                "Question details": [
                    "Question:", question_history.question["question"], "",
                    "Confidence:", question_history.response["confidence"], "",
                    "Explanation:", question_history.response["explanation"],
                ]
            })  # Adjust text as needed
            df_details.to_excel(excel_writer=writer,
                                index=False,
                                sheet_name='Question Details')

        else:
            json = question_history.response["data"]
            df = pd.read_json(json)
            df.to_excel(excel_writer=writer, index=False, sheet_name='Your Translations')

        writer.close()

        # Flask create response
        r = make_response(out.getvalue())

        timestr = strftime('%Y%m%d')

        # Defining correct Excel headers
        r.headers["Content-Disposition"] = f"attachment; filename=export_{timestr}_{dialog_id[:5]}.xlsx"
        r.headers["Content-type"] = "application/x-xls"
    except Exception as e:
        current_app.logger.error(e)

    return r


@rbot.route("/download-file", methods = ["GET"])
@login_api
def download_files():

    try:
        cfg = current_app.config["AI_CONFIG"]
        file_path= request.args.get('path').replace("$", "\\")
        file_path = cfg.pride_product_root + file_path
        file_path = os.path.join(file_path)

        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            abort(404)
    except Exception as e:
        current_app.logger.error(e)

@rbot.route("/download-ptp", methods = ["GET"])
@login_api
def download_ptp():
    try:

        prefix = request.args.get('prefix')
        tech_drawing_code = request.args.get('drawing').replace("$", "\\")

        file_types_supported = ['pdf', 'dxf', 'doc', 'xls', '3dxml', 'zip', 'json', 'jpg']
        zip_buffer = io.BytesIO()

        session_http = requests.Session()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_type in file_types_supported:
                file_url = f'http://doc-smarteam-eu.int.electroluxprofessional.com/Documenti/{file_type}/{prefix if prefix else ''}{tech_drawing_code}.{file_type}'

                response = session_http.get(file_url, stream=True)
                if response.status_code == 404:
                    continue
                elif response.status_code != 200:
                    response.raise_for_status()
                else:
                    zip_file.writestr(f'{tech_drawing_code}.{file_type}', response.content)

        zip_buffer.seek(0)

        return send_file(
            zip_buffer,
            as_attachment=True,
            download_name=f'{tech_drawing_code}.zip',
            mimetype='application/zip'
        )

    except Exception as e:
        current_app.logger.error(e)


@rbot.route("/user-profiles", methods=["GET"])
@login_api
def user_profiles():
    u = session["user"]
    sample_questions = get_sample_questions(db.session, BotType.COMPLI_BOT)
    profile = UserProfile(u, session["user_role"], sample_questions)
    return [profile.to_json()]


@rbot.route("/chat-sessions/<path:conversation_id>", methods=["DELETE"])
@login_api
def chat_sessions(conversation_id: str):
    if is_valid_conversation(conversation_id, session):
        session.pop(get_conversation_session_id(session["user"]["id"], conversation_id))
        
        delete_generated_images_dir()
        return Response(status=200)
    else:
        raise SecurityException("Cannot find an answer due to conversation error")


@rbot.route("/search-settings", methods=["GET"])
@login_api
def route_search_settings():
    u = session["user"]
    user_db = User.query.get(u["id"])
    settings_json = user_db.get_user_settings_json()

    return jsonify(settings_json), 200


@rbot.route("/version", methods=['GET', 'POST'])
def route_get_version():
    try:
        if 'verbose' == request.args.get('botlog'):
            if request.method == 'POST':
                current_app.logger.info(request)
                current_app.logger.info(request.headers)
                current_app.logger.info(request.data)
            elif request.method == 'GET':
                current_app.logger.info(f'### {request.headers}')
    except Exception as e:
        current_app.logger.error(f'Unable to print request -> {e}')

    return get_env_version()


@rbot.route("/changelog-preview", methods=['GET'])
def route_changelog_preview():
    try:
        lines = stream_last_n_releases("changelog.md")
        return {"preview": "".join(lines)}, 200
    except Exception as e:
        current_app.logger.error(f"Unable to read changelog.md: {e}")
        return {"error": "Unable to read changelog"}, 500


@rbot.route("/chat-feedback", methods=["POST"])
@login_api
def chat_feedback():
    u = session["user"]

    cid = request.json.get("conversation_id")
    did = request.json.get("dialog_id")
    feedback = AnswerFeedback[request.json.get("feedback")]

    update_feedback(u["id"], cid, did, feedback=feedback)
    return "OK"


@rbot.route("/bot-list", methods=["GET"])
@login_api
def route_bot_list():
    u = session["user"]
    user_db = User.query.filter(User.email.ilike(u["email"])).first()
    bots_allowed = user_get_allowed_bots(user_db)

    return BotTypeResponse(bots_allowed).to_json()


@rbot.route("/bot-change", methods=["POST"])
@login_api
def route_bot_change():
    u = session["user"]
    user_db = User.query.filter(User.email.ilike(u["email"])).first()
    bots_allowed = user_get_allowed_bots(user_db)
    requested_bot = BotType[request.json.get("bot")]
    if requested_bot in bots_allowed:
        session["selected_bot"] = requested_bot.name
    else:
        return GenericResponse(status=ResponseStatus.ERROR).to_json(), 403

    return GenericResponse(status=ResponseStatus.OK).to_json()


@rbot.route("/upload", methods=["POST"])
def upload_file():
    if not os.path.exists(UPLOAD_FOLDER):
        os.makedirs(UPLOAD_FOLDER)
        current_app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

    data = request.get_json()

    if not data or 'fileContent' not in data or 'fileName' not in data or 'fileType' not in data:
        return jsonify({'error': 'Dati del file mancanti'}), 400

    file_content = data['fileContent']
    file_name = data['fileName']
    file_type = data['fileType']

    try:
        u = session["user"]
        user_db = User.query.filter(User.email.ilike(u["email"])).first()
        if not user_db:
            return jsonify({'error': 'Utente non trovato'}), 404

        extension = os.path.splitext(file_name)[1]
        filename = f"data_{user_db.id}{extension}"
        filepath = os.path.join(UPLOAD_FOLDER, filename)

        with open(filepath, "wb") as file:
            file.write(base64.b64decode(file_content))

        return jsonify({'message': 'File caricato con successo', 'file_path': filepath}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@rbot.route('/admin/loglevel', methods=['POST'])
@login_api
@admin_required
def set_log_level():
    data = request.json
    logger_name = data.get('logger', '')
    level_name = data.get('level', 'INFO').upper()

    # Get the numeric level
    level = getattr(logging, level_name, logging.INFO)

    # Get the logger
    logger = logging.getLogger(logger_name)

    # Set level on logger AND all handlers
    logger.setLevel(level)
    for handler in logger.handlers:
        handler.setLevel(level)

    return jsonify({
        "message": f"Log level for '{logger_name}' set to {level_name}",
        "current_level": logging.getLevelName(logger.getEffectiveLevel())
    }), 200
