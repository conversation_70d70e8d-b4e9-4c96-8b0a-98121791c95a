import re
import time
from concurrent.futures import wait
from enum import StrEnum, auto
from os.path import dirname, join
from threading import current_thread
from typing import List, Optional, Tuple
from pydantic import BaseModel, Field
from openai import AzureOpenAI
import pandas as pd

from config.config import CommonConfig
from src.agents.agent import Agent
from src.agents.answer import Answer
from src.agents.rag_agent.llm_models.metadata_extractor import MetadataExtractor
from src.agents.text_to_sql_agent.database.connection import DatabaseConnection
from src.agents.text_to_sql_agent.database.relevant_schemas import get_relevant_schemas
from src.agents.text_to_sql_agent.database.search_examples import ExampleFinder
from src.agents.text_to_sql_agent.models.llm import LLM
from src.agents.text_to_sql_agent.sql_errors import SQLErrors
from src.backend.contracts.chat_data import AgentName, BotType, UserRole
from src.bots.conversation_session import ConversationSession
from src.common_tools.history.history import ConversationHistory
from utils.core import get_logger
from utils.exceptions import IncapableException



logger = get_logger(__file__)
FILE_DIR = dirname(__file__)
SCHEMA_PATH = join(dirname(__file__), "./models/schema.json")


def equals(df1: pd.DataFrame, df2: pd.DataFrame) -> bool:
    try:
        pd.testing.assert_frame_equal(df1, df2, check_like=True)
        return True
    except AssertionError:
        return False


class ExecutionMode(StrEnum):
    TEST = auto()
    INFERENCE = auto()


class TextToSQLAnswer(Answer):
    data: List[Tuple[str]] = None
    confidence: float = None
    sql: str = None
    explanation: str = None
    showFollowupQuestions = True
    agent_name: str = AgentName.TEXT_TO_SQL.value

    def __init__(self, data: List[Tuple[str]]) -> None:
        self.data = data

    def add_confidence(self, confidence: float) -> None:
        self.confidence = confidence

    def add_sql(self, sql: str) -> None:
        self.sql = sql or ""

    def add_explanation(self, explanation: str) -> None:
        self.explanation = explanation

    def to_json(self):
        return {
            "data": self.data,
            "confidence": self.confidence,
            "sql": self.sql,
            "explanation": self.explanation,
            "agent": self.agent_name
        }


class text_to_sql(BaseModel):
    """This function should be called when the user asks questions that require extracting structured data from a database. Typical queries might include:

        - Requests for specific information based on criteria ("List all the machines  model, pnc and serial number  commissioned and installed on 'Italy'").
        - Requests for aggregations or statistics ("Give me the number of codes present in the agreement B02400K0 with the company code ITP").

       The input question is about the following arguments: Agreements, Items, Compliance, Suppliers, Join-I-A-C-S, Product, BOM, SerialNumber, InstalledBase, Commercial Products.
    """
    query: str =  Field(description=" The question that need to be converted into a SQL query")

class implosion_query(BaseModel):
    """This function should be called when the user asks questions that require extracting structured data from a database. In particular :

        - Requests for parents item codes given the child item code.
        
       The input question is about the following arguments: Agreements, Items, Compliance, Suppliers, Join-I-A-C-S, Product, BOM, SerialNumber, InstalledBase, Commercial Products.
    """
    query: str =  Field(description=" The question that need to recover parents item codes from the child item code")


class Text2SQL(Agent):
    def __init__(self, bot_name: str, name= AgentName.TEXT_TO_SQL.value, azure_model = None, model = None, embedder_model= None, knowledge_base = None) -> None:
        """
        Initialises the Text2SQL Agent, setting up the preliminary model, the embedders, the example finder, and the LLM used.
        """
        super().__init__(name, bot_name)
        logger.debug("Init Text2SQL clients")
        self.examples = ExampleFinder(embedder_model, azure_model)
        self.llm = LLM(
            join(FILE_DIR, "./models/prompt_skeleton.json"),
            get_relevant_schemas([], path=BotType[bot_name].db_schema_path),
            azure_model
        )
        self.database = DatabaseConnection()
        self.common_config = CommonConfig()
        self.max_retries = 0
        self.sql_errors = SQLErrors()
        self.metadata_extractor = MetadataExtractor(model)
        logger.info("Text2SQL agent init completed")

    def clarify(self, inquiry: str) -> str:
        return self.llm.clarify(inquiry)

    def generate_query_candidates(
        self, inquiry: str, history: ConversationHistory, specific_query:bool, n: int = 5
    ) -> List[str]:
        """
        Returns a list with `n` candidate queries that aim to answer `inquiry`
        """
        inquiries = inquiry

        for message in history.history_messages(self.agent_name):
            if message["role"] == "user":
                inquiries += f"\n{message["content"]}"

        if specific_query:
            tagged_bot = self.bot_name + "_IMPLOSION"
            examples = self.examples.search(inquiries, bot=tagged_bot)
        else:
            examples = self.examples.search(inquiries, bot=BotType[self.bot_name])

        self.session.checkpoint()

        examples = [(example["inquiry"], example["sql"]) for example in examples]
        # Build prompt for llm
        prompt = self.llm.get_prompt(examples, inquiry, self.sql_errors, not history.is_empty(), self.bot_name)

        start = time.time()
        # Interrogating the llm
        if history.is_empty():
            executor = self.session.get_pool_executor(
                n, name=current_thread().name + "_llm"
            )

            candidates = [
                executor.submit(self.llm.generate_query, prompt) for _ in range(n)
            ]
            candidates = wait(candidates)
            candidates = [candidate.result() for candidate in iter(candidates.done)]
        else:
            candidates = [self.llm.keep_chatting(prompt, history)]

        end = time.time()
        logger.info(f"Elapsed time for SQL query generation: {end-start:0.3f}s")

        self.session.checkpoint()

        return candidates

    def _majority_voting(
        candidates: List[Tuple[str, List[Tuple]]]
    ) -> Tuple[str, List[Tuple[str]], int]:
        """
        Returns the majority candidate among the provided candidates with its majority (number of ocurrances in `candidates`).
        """
        # Transform data into dataframes
        results = [
            (
                candidate[0],
                pd.DataFrame.from_records(
                    data=candidate[1][1:], columns=candidate[1][0]
                ),
            )
            for candidate in candidates
        ]
        # Cast type of all data acquired to be a string
        results = [
            (result[0], result[1].astype({col: str for col in result[1].columns}))
            for result in results
        ]

        # Initialise majority voting
        winner = results[0]
        count = 0
        # Determine majority element
        for result in results:
            if count == 0:
                logger.debug(f"New majority query elected:\n{result[0]}")
                winner = result
                count = 1
            elif equals(winner[1], result[1]):
                logger.debug(f"{winner[0]} has been confirmed by:\n{result[0]}")
                count += 1
            else:
                logger.debug(f"{winner[0]} has been confuted by:\n{result[0]}")
                count -= 1
        # Determine magnitude of majority
        count = 0
        for result in results:
            if equals(result[1], winner[1]):
                count += 1

        cols = [tuple(winner[1].columns)]
        rows = list(winner[1].itertuples(index=False, name=None))

        return winner[0], cols + rows, count

    def ask(
        self, inquiry: str, history: ConversationHistory, max_rows: Optional[int] = 10, conversation_session: ConversationSession = None, show_explanation:bool = False, show_sql:bool = False, specific_query: bool = False
    ) -> TextToSQLAnswer:
        """Interrogates the bot with a specific inquiry. Enriches the inquiry to submit it to a LLM which generates an SQL query that is then validated by the database.

        Args:
            inquiry (str): The question in natural language posed by the user.
            self_correction (Optional[bool], optional): Whether or not to use self-correction. True to use it, False to not use it. Defaults to False.

        Raises:
            IncapableException: It is raised when the final query produced by the LLM fails database validation.

        Returns:
            Answer: An object containing all relevant values for the answer.
        """
        self.session = conversation_session

        self.sql_errors.reset()

        if specific_query:
            # Prepare SQL candidates
            candidates = self.generate_query_candidates(inquiry, history, specific_query)

        else:
             
            # Prepare SQL candidates
            candidates = self.generate_query_candidates(inquiry, history, specific_query)

        self.session.checkpoint()

        start = time.time()
        # Keep only valid candidates and fetch results
        executor = self.session.get_pool_executor(
            len(candidates), name=current_thread().name + "_database"
        )

        self.session.checkpoint()

        candidates = self.execute_candidates(candidates, executor, max_rows)

        valid_candidates = self.validate_candidates(candidates)
        
        end = time.time()
        logger.info(f"Elapsed time for SQL query validation: {end - start:0.3f}s")

        # Select the best candidate (if there are any)
        if valid_candidates:
            answer = self.construct_TextToSQL_Answer(candidates, valid_candidates, show_explanation, show_sql)
            return answer
        else:
            for max_retries in range(0,3):
                self.sql_errors.check_invalid_identifier_error(candidates)
                candidates = self.generate_query_candidates(inquiry, history, specific_query)
                candidates = self.execute_candidates(candidates, executor, max_rows)
                valid_candidates = self.validate_candidates(candidates)

                if valid_candidates:
                    answer = self.construct_TextToSQL_Answer(candidates, valid_candidates, show_explanation, show_sql)
                    return answer
                elif max_retries == 2:
                    raise IncapableException(
                        f'Cannot find an answer to: "{inquiry}"',
                        candidates if self.common_config.default_run_mode == "TEST" else None,
                    )

            
            
    def validate_candidates(self, candidates: list[Tuple[str, List[Tuple] | str]]) -> List:
       return [ candidate for candidate in candidates if type(candidate[1]) != str ]
    
    def execute_candidates(self, candidates, executor, max_rows):
        candidates = [candidate.strip("```").strip("```").replace("sql ", "", 1).rstrip() for candidate in candidates]
        candidates = [
            executor.submit(self.database.validate, candidate, max_rows)
            for candidate in candidates
        ]
        candidates = wait(candidates)
        candidates = [candidate.result() for candidate in iter(candidates.done)]

        return candidates
    
    def construct_TextToSQL_Answer(self, candidates: list[Tuple[str, List[Tuple] | str]], valid_candidates:List, show_explanation:bool, show_sql:bool) -> TextToSQLAnswer:
        winner_sql, winner_results, majority = Text2SQL._majority_voting(
                valid_candidates
            )
        confidence = majority / len(candidates)
        # TODO - Add the following when history is to be implemented.
        # self.session.add_interaction(inquiry, winner_sql)
        
        logger.info(f"SQL elected with confidence {confidence}:\n{winner_sql}")

        # Build answer
        answer = TextToSQLAnswer(winner_results)
        answer.add_confidence(confidence)
        if show_explanation:
            answer.add_explanation(self.llm.explain_query(winner_sql))
        answer.add_agent_name(self.agent_name)

        if show_sql:
            answer.add_sql(winner_sql)

        return answer
            
    def shutdown(self):
        """
        Close every connection and stop the bot.
        """
        self.database.close()

