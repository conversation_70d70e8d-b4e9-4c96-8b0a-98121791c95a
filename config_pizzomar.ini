[txt2sql.oracle_oci]
oci_client_path = C:\DBEAVER_OCI\instantclient-basic-windows.x64-21.18.0.0.0dbru\instantclient_21_18
oci_dsn = dwhtest_high

[rag.parameters]
embedding_resource = https://epr-openai-sandbox-plus.openai.azure.com/
embedding_model = embedding-test-rag

[call_center_bot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = it-documents-pride-dev

[common]
run_mode = TEST
logout_url = https://electroluxprofessional.unily.com

[call_center_bot.rag.fetcher]
local_root =
pride_products_root = \\fs_prod.epr.electroluxprofessional.com\pride_fs_prod\Attach\
pride_document_types = PDS 

[how_to_bot.rag.fetcher]
local_root = ""
pride_products_root = 

[jdanallo.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = jde-documents

[jdanallo.rag.fetcher]
local_root = C:\Users\<USER>\JDE_Documentation
pride_products_root = 

[jdanallo.rag.param]
semantic_configuration = semantic-jde
vector_search_profile = vector-profile-jde

[seobot.rag.fetcher]
local_root = C:\Users\<USER>\SEO_Documentation
pride_products_root = 

[seobot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = seo-bot-documents

[seobot.rag.param]
semantic_configuration = semantic-seo
vector_search_profile = vector-profile-seo

[log]
loglevel = DEBUG
log_folder = .

[ad]
ad_schema_callback=http

[db]
db_driver = ODBC Driver 17 for SQL Server

[call_center_bot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = pride-tables-rag-dev
image_container = pride-images-rag-dev

[jdanallo.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = jdallo-tables-rag-dev
image_container = jdallo-images-rag-dev

[seobot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = seo-bot-tables-rag
image_container = seo-bot-images-rag

[image.generator]
endpoint = https://epr002-oai-test-dev-wus3-001.openai.azure.com/
model = gpt-image-1
temperature = 0.25
version = 2025-04-01-preview

[image_generator]
api_llm_endpoint = 	https://epr002-oai-test-dev-wus3-001.openai.azure.com/
llm_deployed = gpt-image-1
api_version = 2025-04-01-preview